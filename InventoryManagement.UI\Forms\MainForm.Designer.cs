namespace InventoryManagement.UI.Forms
{
    partial class MainForm
    {
        private System.ComponentModel.IContainer components = null;
        private MenuStrip menuStrip;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem helpToolStripMenuItem;
        private ToolStripMenuItem aboutToolStripMenuItem;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private Panel pnlSidebar;
        private Panel pnlMain;
        private Panel pnlDashboard;
        private Label lblWelcome;
        private Label lblRole;
        private Label lblTotalProducts;
        private Label lblLowStock;
        private Label lblInventoryValue;
        private Button btnProducts;
        private Button btnCategories;
        private Button btnSuppliers;
        private Button btnCustomers;
        private Button btnPurchaseOrders;
        private Button btnSalesOrders;
        private Button btnReports;
        private Button btnSettings;
        private Button btnLogout;
        private System.Windows.Forms.Timer refreshTimer;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.menuStrip = new MenuStrip();
            this.fileToolStripMenuItem = new ToolStripMenuItem();
            this.exitToolStripMenuItem = new ToolStripMenuItem();
            this.helpToolStripMenuItem = new ToolStripMenuItem();
            this.aboutToolStripMenuItem = new ToolStripMenuItem();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.pnlSidebar = new Panel();
            this.pnlMain = new Panel();
            this.pnlDashboard = new Panel();
            this.lblWelcome = new Label();
            this.lblRole = new Label();
            this.lblTotalProducts = new Label();
            this.lblLowStock = new Label();
            this.lblInventoryValue = new Label();
            this.btnProducts = new Button();
            this.btnCategories = new Button();
            this.btnSuppliers = new Button();
            this.btnCustomers = new Button();
            this.btnPurchaseOrders = new Button();
            this.btnSalesOrders = new Button();
            this.btnReports = new Button();
            this.btnSettings = new Button();
            this.btnLogout = new Button();
            this.refreshTimer = new System.Windows.Forms.Timer(this.components);
            
            this.menuStrip.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.pnlSidebar.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.pnlDashboard.SuspendLayout();
            this.SuspendLayout();
            
            // menuStrip
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                this.fileToolStripMenuItem,
                this.helpToolStripMenuItem});
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new Size(1200, 24);
            this.menuStrip.TabIndex = 0;
            
            // fileToolStripMenuItem
            this.fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.exitToolStripMenuItem});
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new Size(37, 20);
            this.fileToolStripMenuItem.Text = "File";
            
            // exitToolStripMenuItem
            this.exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            this.exitToolStripMenuItem.Size = new Size(93, 22);
            this.exitToolStripMenuItem.Text = "Exit";
            this.exitToolStripMenuItem.Click += new EventHandler(this.btnLogout_Click);
            
            // helpToolStripMenuItem
            this.helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.aboutToolStripMenuItem});
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new Size(44, 20);
            this.helpToolStripMenuItem.Text = "Help";
            
            // aboutToolStripMenuItem
            this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            this.aboutToolStripMenuItem.Size = new Size(107, 22);
            this.aboutToolStripMenuItem.Text = "About";
            
            // statusStrip
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel});
            this.statusStrip.Location = new Point(0, 676);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1200, 22);
            this.statusStrip.TabIndex = 1;
            
            // statusLabel
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new Size(39, 17);
            this.statusLabel.Text = "Ready";
            
            // pnlSidebar
            this.pnlSidebar.BackColor = Color.FromArgb(52, 58, 64);
            this.pnlSidebar.Controls.Add(this.lblWelcome);
            this.pnlSidebar.Controls.Add(this.lblRole);
            this.pnlSidebar.Controls.Add(this.btnProducts);
            this.pnlSidebar.Controls.Add(this.btnCategories);
            this.pnlSidebar.Controls.Add(this.btnSuppliers);
            this.pnlSidebar.Controls.Add(this.btnCustomers);
            this.pnlSidebar.Controls.Add(this.btnPurchaseOrders);
            this.pnlSidebar.Controls.Add(this.btnSalesOrders);
            this.pnlSidebar.Controls.Add(this.btnReports);
            this.pnlSidebar.Controls.Add(this.btnSettings);
            this.pnlSidebar.Controls.Add(this.btnLogout);
            this.pnlSidebar.Dock = DockStyle.Left;
            this.pnlSidebar.Location = new Point(0, 24);
            this.pnlSidebar.Name = "pnlSidebar";
            this.pnlSidebar.Size = new Size(250, 652);
            this.pnlSidebar.TabIndex = 2;
            
            // lblWelcome
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblWelcome.ForeColor = Color.White;
            this.lblWelcome.Location = new Point(20, 20);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.Size = new Size(76, 21);
            this.lblWelcome.TabIndex = 0;
            this.lblWelcome.Text = "Welcome";
            
            // lblRole
            this.lblRole.AutoSize = true;
            this.lblRole.Font = new Font("Segoe UI", 9F);
            this.lblRole.ForeColor = Color.LightGray;
            this.lblRole.Location = new Point(20, 45);
            this.lblRole.Name = "lblRole";
            this.lblRole.Size = new Size(30, 15);
            this.lblRole.TabIndex = 1;
            this.lblRole.Text = "Role";
            
            // Create sidebar buttons
            this.btnProducts = CreateSidebarButton("Products", 80);
            this.btnCategories = CreateSidebarButton("Categories", 120);
            this.btnSuppliers = CreateSidebarButton("Suppliers", 160);
            this.btnCustomers = CreateSidebarButton("Customers", 200);
            this.btnPurchaseOrders = CreateSidebarButton("Purchase Orders", 240);
            this.btnSalesOrders = CreateSidebarButton("Sales Orders", 280);
            this.btnReports = CreateSidebarButton("Reports", 320);
            this.btnSettings = CreateSidebarButton("Settings", 360);
            this.btnLogout = CreateSidebarButton("Logout", 580);
            
            this.btnProducts.Click += new EventHandler(this.btnProducts_Click);
            this.btnCategories.Click += new EventHandler(this.btnCategories_Click);
            this.btnSuppliers.Click += new EventHandler(this.btnSuppliers_Click);
            this.btnCustomers.Click += new EventHandler(this.btnCustomers_Click);
            this.btnPurchaseOrders.Click += new EventHandler(this.btnPurchaseOrders_Click);
            this.btnSalesOrders.Click += new EventHandler(this.btnSalesOrders_Click);
            this.btnReports.Click += new EventHandler(this.btnReports_Click);
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);
            this.btnLogout.Click += new EventHandler(this.btnLogout_Click);
            this.btnLogout.BackColor = Color.FromArgb(220, 53, 69);
            
            // pnlMain
            this.pnlMain.Controls.Add(this.pnlDashboard);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(250, 24);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new Size(950, 652);
            this.pnlMain.TabIndex = 3;
            
            // pnlDashboard
            this.pnlDashboard.BackColor = Color.White;
            this.pnlDashboard.Controls.Add(this.lblTotalProducts);
            this.pnlDashboard.Controls.Add(this.lblLowStock);
            this.pnlDashboard.Controls.Add(this.lblInventoryValue);
            this.pnlDashboard.Dock = DockStyle.Fill;
            this.pnlDashboard.Location = new Point(0, 0);
            this.pnlDashboard.Name = "pnlDashboard";
            this.pnlDashboard.Size = new Size(950, 652);
            this.pnlDashboard.TabIndex = 0;
            
            // Dashboard labels
            this.lblTotalProducts.AutoSize = true;
            this.lblTotalProducts.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblTotalProducts.ForeColor = Color.FromArgb(40, 167, 69);
            this.lblTotalProducts.Location = new Point(50, 50);
            this.lblTotalProducts.Name = "lblTotalProducts";
            this.lblTotalProducts.Size = new Size(140, 25);
            this.lblTotalProducts.TabIndex = 0;
            this.lblTotalProducts.Text = "Total Products: 0";
            
            this.lblLowStock.AutoSize = true;
            this.lblLowStock.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblLowStock.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblLowStock.Location = new Point(50, 100);
            this.lblLowStock.Name = "lblLowStock";
            this.lblLowStock.Size = new Size(150, 25);
            this.lblLowStock.TabIndex = 1;
            this.lblLowStock.Text = "Low Stock Items: 0";
            
            this.lblInventoryValue.AutoSize = true;
            this.lblInventoryValue.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblInventoryValue.ForeColor = Color.FromArgb(0, 123, 255);
            this.lblInventoryValue.Location = new Point(50, 150);
            this.lblInventoryValue.Name = "lblInventoryValue";
            this.lblInventoryValue.Size = new Size(170, 25);
            this.lblInventoryValue.TabIndex = 2;
            this.lblInventoryValue.Text = "Inventory Value: $0.00";
            
            // refreshTimer
            this.refreshTimer.Interval = 30000; // 30 seconds
            this.refreshTimer.Tick += new EventHandler(this.refreshTimer_Tick);
            this.refreshTimer.Start();
            
            // MainForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 700);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlSidebar);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.menuStrip);
            this.IsMdiContainer = true;
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.Text = "Inventory Management System";
            this.WindowState = FormWindowState.Maximized;
            this.Load += new EventHandler(this.MainForm_Load);
            
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.pnlSidebar.ResumeLayout(false);
            this.pnlSidebar.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            this.pnlDashboard.ResumeLayout(false);
            this.pnlDashboard.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private Button CreateSidebarButton(string text, int top)
        {
            var button = new Button();
            button.BackColor = Color.FromArgb(73, 80, 87);
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            button.ForeColor = Color.White;
            button.Location = new Point(20, top);
            button.Name = $"btn{text.Replace(" ", "")}";
            button.Size = new Size(210, 35);
            button.TabIndex = 2;
            button.Text = text;
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.UseVisualStyleBackColor = false;
            button.MouseEnter += (s, e) => button.BackColor = Color.FromArgb(108, 117, 125);
            button.MouseLeave += (s, e) => button.BackColor = Color.FromArgb(73, 80, 87);
            this.pnlSidebar.Controls.Add(button);
            return button;
        }
    }
}
