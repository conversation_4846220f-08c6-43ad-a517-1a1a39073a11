namespace InventoryManagement.UI.Forms
{
    partial class ProductsForm
    {
        private System.ComponentModel.IContainer components = null;
        private Panel pnlTop;
        private Panel pnlMain;
        private DataGridView dgvProducts;
        private TextBox txtSearch;
        private Label lblSearch;
        private ComboBox cmbCategory;
        private Label lblCategory;
        private ComboBox cmbSupplier;
        private Label lblSupplier;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnShowAll;
        private Button btnLowStock;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.pnlTop = new Panel();
            this.pnlMain = new Panel();
            this.dgvProducts = new DataGridView();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.cmbCategory = new ComboBox();
            this.lblCategory = new Label();
            this.cmbSupplier = new ComboBox();
            this.lblSupplier = new Label();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnShowAll = new Button();
            this.btnLowStock = new Button();
            
            this.pnlTop.SuspendLayout();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).BeginInit();
            this.SuspendLayout();
            
            // pnlTop
            this.pnlTop.BackColor = Color.White;
            this.pnlTop.BorderStyle = BorderStyle.FixedSingle;
            this.pnlTop.Controls.Add(this.lblSearch);
            this.pnlTop.Controls.Add(this.txtSearch);
            this.pnlTop.Controls.Add(this.lblCategory);
            this.pnlTop.Controls.Add(this.cmbCategory);
            this.pnlTop.Controls.Add(this.lblSupplier);
            this.pnlTop.Controls.Add(this.cmbSupplier);
            this.pnlTop.Controls.Add(this.btnAdd);
            this.pnlTop.Controls.Add(this.btnEdit);
            this.pnlTop.Controls.Add(this.btnDelete);
            this.pnlTop.Controls.Add(this.btnShowAll);
            this.pnlTop.Controls.Add(this.btnLowStock);
            this.pnlTop.Dock = DockStyle.Top;
            this.pnlTop.Location = new Point(0, 0);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new Size(1000, 80);
            this.pnlTop.TabIndex = 0;
            
            // lblSearch
            this.lblSearch.AutoSize = true;
            this.lblSearch.Font = new Font("Segoe UI", 9F);
            this.lblSearch.Location = new Point(20, 15);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new Size(45, 15);
            this.lblSearch.TabIndex = 0;
            this.lblSearch.Text = "Search:";
            
            // txtSearch
            this.txtSearch.Font = new Font("Segoe UI", 9F);
            this.txtSearch.Location = new Point(20, 35);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.PlaceholderText = "Search by name, SKU, or barcode...";
            this.txtSearch.Size = new Size(200, 23);
            this.txtSearch.TabIndex = 1;
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);
            
            // lblCategory
            this.lblCategory.AutoSize = true;
            this.lblCategory.Font = new Font("Segoe UI", 9F);
            this.lblCategory.Location = new Point(240, 15);
            this.lblCategory.Name = "lblCategory";
            this.lblCategory.Size = new Size(58, 15);
            this.lblCategory.TabIndex = 2;
            this.lblCategory.Text = "Category:";
            
            // cmbCategory
            this.cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCategory.Font = new Font("Segoe UI", 9F);
            this.cmbCategory.FormattingEnabled = true;
            this.cmbCategory.Location = new Point(240, 35);
            this.cmbCategory.Name = "cmbCategory";
            this.cmbCategory.Size = new Size(150, 23);
            this.cmbCategory.TabIndex = 3;
            this.cmbCategory.SelectedIndexChanged += new EventHandler(this.cmbCategory_SelectedIndexChanged);
            
            // lblSupplier
            this.lblSupplier.AutoSize = true;
            this.lblSupplier.Font = new Font("Segoe UI", 9F);
            this.lblSupplier.Location = new Point(410, 15);
            this.lblSupplier.Name = "lblSupplier";
            this.lblSupplier.Size = new Size(53, 15);
            this.lblSupplier.TabIndex = 4;
            this.lblSupplier.Text = "Supplier:";
            
            // cmbSupplier
            this.cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbSupplier.Font = new Font("Segoe UI", 9F);
            this.cmbSupplier.FormattingEnabled = true;
            this.cmbSupplier.Location = new Point(410, 35);
            this.cmbSupplier.Name = "cmbSupplier";
            this.cmbSupplier.Size = new Size(150, 23);
            this.cmbSupplier.TabIndex = 5;
            
            // btnAdd
            this.btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.Location = new Point(580, 15);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new Size(80, 30);
            this.btnAdd.TabIndex = 6;
            this.btnAdd.Text = "Add";
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new EventHandler(this.btnAdd_Click);
            
            // btnEdit
            this.btnEdit.BackColor = Color.FromArgb(0, 123, 255);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(670, 15);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new Size(80, 30);
            this.btnEdit.TabIndex = 7;
            this.btnEdit.Text = "Edit";
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);
            
            // btnDelete
            this.btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(760, 15);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new Size(80, 30);
            this.btnDelete.TabIndex = 8;
            this.btnDelete.Text = "Delete";
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);
            
            // btnShowAll
            this.btnShowAll.BackColor = Color.FromArgb(108, 117, 125);
            this.btnShowAll.FlatStyle = FlatStyle.Flat;
            this.btnShowAll.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnShowAll.ForeColor = Color.White;
            this.btnShowAll.Location = new Point(580, 50);
            this.btnShowAll.Name = "btnShowAll";
            this.btnShowAll.Size = new Size(80, 25);
            this.btnShowAll.TabIndex = 9;
            this.btnShowAll.Text = "Show All";
            this.btnShowAll.UseVisualStyleBackColor = false;
            this.btnShowAll.Click += new EventHandler(this.btnShowAll_Click);
            
            // btnLowStock
            this.btnLowStock.BackColor = Color.FromArgb(255, 193, 7);
            this.btnLowStock.FlatStyle = FlatStyle.Flat;
            this.btnLowStock.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnLowStock.ForeColor = Color.Black;
            this.btnLowStock.Location = new Point(670, 50);
            this.btnLowStock.Name = "btnLowStock";
            this.btnLowStock.Size = new Size(80, 25);
            this.btnLowStock.TabIndex = 10;
            this.btnLowStock.Text = "Low Stock";
            this.btnLowStock.UseVisualStyleBackColor = false;
            this.btnLowStock.Click += new EventHandler(this.btnLowStock_Click);
            
            // pnlMain
            this.pnlMain.Controls.Add(this.dgvProducts);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(0, 80);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new Size(1000, 520);
            this.pnlMain.TabIndex = 1;
            
            // dgvProducts
            this.dgvProducts.AllowUserToAddRows = false;
            this.dgvProducts.AllowUserToDeleteRows = false;
            this.dgvProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvProducts.BackgroundColor = Color.White;
            this.dgvProducts.BorderStyle = BorderStyle.None;
            this.dgvProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvProducts.Dock = DockStyle.Fill;
            this.dgvProducts.Location = new Point(0, 0);
            this.dgvProducts.MultiSelect = false;
            this.dgvProducts.Name = "dgvProducts";
            this.dgvProducts.ReadOnly = true;
            this.dgvProducts.RowHeadersVisible = false;
            this.dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvProducts.Size = new Size(1000, 520);
            this.dgvProducts.TabIndex = 0;
            this.dgvProducts.CellDoubleClick += new DataGridViewCellEventHandler(this.dgvProducts_CellDoubleClick);
            
            // ProductsForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 600);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlTop);
            this.Name = "ProductsForm";
            this.Text = "Products Management";
            
            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).EndInit();
            this.ResumeLayout(false);
        }
    }
}
