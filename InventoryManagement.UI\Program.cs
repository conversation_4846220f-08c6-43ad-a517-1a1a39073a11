using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using InventoryManagement.Data;
using InventoryManagement.Data.Repositories;
using InventoryManagement.Business.Services;
using InventoryManagement.UI.Forms;

namespace InventoryManagement.UI
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            var host = CreateHostBuilder().Build();
            
            // Initialize database
            using (var scope = host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<InventoryDbContext>();
                context.Database.EnsureCreated();
            }

            var serviceProvider = host.Services;
            Application.Run(new LoginForm(serviceProvider));
        }

        static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // Database
                    services.AddDbContext<InventoryDbContext>(options =>
                        options.UseNpgsql(context.Configuration.GetConnectionString("DefaultConnection")));

                    // Repositories
                    services.AddScoped<IUnitOfWork, UnitOfWork>();

                    // Services
                    services.AddScoped<IAuthService, AuthService>();
                    services.AddScoped<IProductService, ProductService>();
                });
        }
    }
}
