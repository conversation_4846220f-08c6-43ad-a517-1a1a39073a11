using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    public class Product
    {
        public int ProductId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string SKU { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [Required]
        public int CategoryId { get; set; }
        
        public int? SupplierId { get; set; }
        
        [StringLength(50)]
        public string? Barcode { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SellingPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? WholesalePrice { get; set; }
        
        public int CurrentStock { get; set; } = 0;
        
        public int MinimumStock { get; set; } = 0;
        
        public int MaximumStock { get; set; } = 1000;
        
        public int ReorderLevel { get; set; } = 10;
        
        [StringLength(20)]
        public string Unit { get; set; } = "PCS";
        
        [Column(TypeName = "decimal(5,2)")]
        public decimal? Weight { get; set; }
        
        [StringLength(100)]
        public string? Dimensions { get; set; }
        
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 0;
        
        public bool IsActive { get; set; } = true;
        
        public bool TrackStock { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        public string? CreatedBy { get; set; }
        
        public DateTime? LastUpdated { get; set; }
        
        // Navigation properties
        public virtual Category Category { get; set; } = null!;
        public virtual Supplier? Supplier { get; set; }
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<PurchaseOrderItem> PurchaseOrderItems { get; set; } = new List<PurchaseOrderItem>();
        public virtual ICollection<SalesOrderItem> SalesOrderItems { get; set; } = new List<SalesOrderItem>();
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; } = new List<StockAdjustment>();
    }
}
