using Microsoft.Extensions.DependencyInjection;

namespace InventoryManagement.UI.Forms
{
    public partial class SuppliersForm : Form
    {
        private readonly IServiceProvider _serviceProvider;

        public SuppliersForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }
    }

    partial class SuppliersForm
    {
        private void InitializeComponent()
        {
            var label = new Label
            {
                Text = "Suppliers Management - Coming Soon",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            
            this.Controls.Add(label);
            this.Text = "Suppliers Management";
            this.Size = new Size(800, 600);
        }
    }
}
