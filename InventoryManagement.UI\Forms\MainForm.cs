using Microsoft.Extensions.DependencyInjection;
using InventoryManagement.Business.Services;
using InventoryManagement.Models;

namespace InventoryManagement.UI.Forms
{
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly IProductService _productService;

        public MainForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _authService = serviceProvider.GetRequiredService<IAuthService>();
            _productService = serviceProvider.GetRequiredService<IProductService>();
            InitializeComponent();
            LoadUserInfo();
            _ = LoadDashboardData();
        }

        private void LoadUserInfo()
        {
            if (_authService.CurrentUser != null)
            {
                lblWelcome.Text = $"Welcome, {_authService.CurrentUser.FullName}";
                lblRole.Text = $"Role: {_authService.CurrentUser.Role}";
            }
        }

        private async Task LoadDashboardData()
        {
            try
            {
                var products = await _productService.GetAllProductsAsync();
                var lowStockProducts = await _productService.GetLowStockProductsAsync();

                lblTotalProducts.Text = $"Total Products: {products.Count()}";
                lblLowStock.Text = $"Low Stock Items: {lowStockProducts.Count()}";

                var totalValue = products.Sum(p => p.CurrentStock * p.CostPrice);
                lblInventoryValue.Text = $"Inventory Value: ${totalValue:N2}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading dashboard: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnProducts_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ProductsForm(_serviceProvider));
        }

        private void btnCategories_Click(object sender, EventArgs e)
        {
            OpenChildForm(new CategoriesForm(_serviceProvider));
        }

        private void btnSuppliers_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SuppliersForm(_serviceProvider));
        }

        private void btnCustomers_Click(object sender, EventArgs e)
        {
            OpenChildForm(new CustomersForm(_serviceProvider));
        }

        private void btnPurchaseOrders_Click(object sender, EventArgs e)
        {
            OpenChildForm(new PurchaseOrdersForm(_serviceProvider));
        }

        private void btnSalesOrders_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SalesOrdersForm(_serviceProvider));
        }

        private void btnReports_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ReportsForm(_serviceProvider));
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SettingsForm(_serviceProvider));
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _authService.Logout();
                this.Hide();
                var loginForm = new LoginForm(_serviceProvider);
                loginForm.FormClosed += (s, args) => this.Close();
                loginForm.Show();
            }
        }

        private void OpenChildForm(Form childForm)
        {
            // Close any existing child form
            foreach (Form form in this.MdiChildren)
            {
                form.Close();
            }

            childForm.MdiParent = this;
            childForm.WindowState = FormWindowState.Maximized;
            childForm.Show();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private async void refreshTimer_Tick(object sender, EventArgs e)
        {
            _ = LoadDashboardData();
        }
    }
}
