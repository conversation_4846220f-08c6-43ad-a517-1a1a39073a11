# Inventory Management System

A complete, production-ready inventory management system built with C# WinForms and PostgreSQL.

## Features (20+ Core Features)

### ✅ Implemented Features
1. **User Management & Authentication** - Login system with role-based access
2. **Product Management** - Complete CRUD operations for products
3. **Category Management** - Product categorization
4. **Supplier Management** - Supplier information management
5. **Customer Management** - Customer database
6. **Stock Management** - Real-time stock tracking
7. **Purchase Order Management** - Purchase order processing
8. **Sales Order Management** - Sales order processing
9. **Inventory Tracking** - Real-time stock levels and movements
10. **Barcode Generation** - Automatic barcode generation
11. **Low Stock Alerts** - Automatic low stock notifications
12. **Dashboard with Analytics** - Real-time dashboard with key metrics
13. **Audit Trail** - Complete audit logging system
14. **Multi-location Support** - Warehouse management
15. **Price Management** - Cost and selling price management
16. **Tax Management** - Tax calculation system
17. **Return Management** - Sales and purchase returns
18. **Advanced Search & Filtering** - Powerful search capabilities
19. **Role-based Access Control** - User permission system
20. **Notification System** - System-wide notifications
21. **Data Validation** - Comprehensive input validation
22. **Stock Adjustments** - Manual stock corrections
23. **Reporting System** - Various business reports
24. **Settings Management** - System configuration

### 🚧 Coming Soon Features
- Backup & Restore functionality
- Export/Import (Excel, CSV)
- Advanced reporting with charts
- Email notifications
- Barcode scanning integration

## Technology Stack

- **Frontend**: C# WinForms (.NET 8)
- **Backend**: C# Business Logic Layer
- **Database**: PostgreSQL with Entity Framework Core
- **Architecture**: 3-tier architecture (Presentation, Business, Data Access)
- **Authentication**: BCrypt password hashing
- **ORM**: Entity Framework Core 8.0

## Prerequisites

1. **.NET 8 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
2. **PostgreSQL** - Download from [PostgreSQL Official Site](https://www.postgresql.org/download/)
3. **Visual Studio 2022** (recommended) or Visual Studio Code

## Installation & Setup

### 1. Install PostgreSQL

1. Download and install PostgreSQL
2. During installation, remember the password for the `postgres` user
3. Default port is `5432`

### 2. Configure Database Connection

1. Open `InventoryManagement.UI/appsettings.json`
2. Update the connection string with your PostgreSQL credentials:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=InventoryManagementDB;Username=postgres;Password=YOUR_PASSWORD_HERE"
  }
}
```

Replace `YOUR_PASSWORD_HERE` with your PostgreSQL password.

### 3. Build and Run

1. Open the solution in Visual Studio 2022
2. Restore NuGet packages (should happen automatically)
3. Build the solution (Ctrl+Shift+B)
4. Set `InventoryManagement.UI` as the startup project
5. Run the application (F5)

The application will automatically create the database and seed initial data on first run.

### 4. Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`

## Project Structure

```
InventoryManagement/
├── InventoryManagement.Models/          # Data models and entities
├── InventoryManagement.Data/            # Data access layer with EF Core
│   ├── Repositories/                    # Repository pattern implementation
│   └── InventoryDbContext.cs           # Database context
├── InventoryManagement.Business/        # Business logic layer
│   └── Services/                       # Business services
├── InventoryManagement.UI/              # WinForms presentation layer
│   └── Forms/                          # All form classes
└── InventoryManagement.sln             # Solution file
```

## Database Schema

The system includes the following main entities:
- Users (Authentication & Authorization)
- Categories (Product categorization)
- Suppliers (Vendor management)
- Customers (Customer database)
- Products (Inventory items)
- Purchase Orders & Items (Procurement)
- Sales Orders & Items (Sales)
- Stock Movements (Inventory tracking)
- Stock Adjustments (Manual corrections)
- Audit Logs (System audit trail)
- Notifications (System alerts)

## Usage Guide

### Getting Started
1. Login with admin credentials
2. Navigate through the sidebar menu
3. Start by setting up Categories and Suppliers
4. Add Products to your inventory
5. Create Purchase Orders to stock inventory
6. Process Sales Orders for customer sales

### Key Features
- **Dashboard**: View real-time inventory metrics
- **Products**: Manage your product catalog with full CRUD operations
- **Search**: Use the powerful search functionality to find products quickly
- **Stock Tracking**: Monitor stock levels and get low stock alerts
- **Order Management**: Process purchase and sales orders efficiently

## Development Notes

### Architecture
- **3-Tier Architecture**: Clean separation of concerns
- **Repository Pattern**: Abstracted data access
- **Dependency Injection**: Loosely coupled components
- **Entity Framework Core**: Code-first database approach

### Security
- **Password Hashing**: BCrypt for secure password storage
- **Role-based Access**: Different user roles with appropriate permissions
- **Audit Trail**: Complete logging of all system changes

### Performance
- **Async/Await**: Non-blocking database operations
- **Efficient Queries**: Optimized Entity Framework queries
- **Connection Pooling**: PostgreSQL connection pooling

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify PostgreSQL is running
   - Check connection string in appsettings.json
   - Ensure database user has proper permissions

2. **Build Errors**
   - Restore NuGet packages: `dotnet restore`
   - Clean and rebuild solution

3. **Login Issues**
   - Use default credentials: admin/admin123
   - Check if database was created properly

### Support
For issues and questions, please check the troubleshooting section above or review the code comments for implementation details.

## License

This project is for educational and commercial use. Feel free to modify and distribute as needed.

## Contributing

This is a complete inventory management system template. You can extend it by:
- Adding more advanced reporting features
- Implementing barcode scanning
- Adding email notifications
- Creating mobile app integration
- Adding more business-specific features

---

**Note**: This system is production-ready but should be thoroughly tested in your specific environment before deployment.
