using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    public class StockMovement
    {
        public int StockMovementId { get; set; }
        
        public int ProductId { get; set; }
        
        public MovementType MovementType { get; set; }
        
        public int Quantity { get; set; }
        
        public int PreviousStock { get; set; }
        
        public int NewStock { get; set; }
        
        [StringLength(100)]
        public string? Reference { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public DateTime MovementDate { get; set; } = DateTime.UtcNow;
        
        public string? CreatedBy { get; set; }
        
        // Navigation properties
        public virtual Product Product { get; set; } = null!;
    }

    public class StockAdjustment
    {
        public int StockAdjustmentId { get; set; }
        
        public int ProductId { get; set; }
        
        public AdjustmentType AdjustmentType { get; set; }
        
        public int Quantity { get; set; }
        
        public int PreviousStock { get; set; }
        
        public int NewStock { get; set; }
        
        [StringLength(500)]
        public string Reason { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CostImpact { get; set; }
        
        public DateTime AdjustmentDate { get; set; } = DateTime.UtcNow;
        
        public string? CreatedBy { get; set; }
        
        // Navigation properties
        public virtual Product Product { get; set; } = null!;
    }

    public enum MovementType
    {
        StockIn = 1,
        StockOut = 2,
        Transfer = 3,
        Adjustment = 4,
        Return = 5,
        Damage = 6,
        Loss = 7
    }

    public enum AdjustmentType
    {
        Increase = 1,
        Decrease = 2,
        Correction = 3
    }
}
