using InventoryManagement.Models;

namespace InventoryManagement.Business.Services
{
    public interface IAuthService
    {
        Task<User?> AuthenticateAsync(string username, string password);
        Task<User> RegisterUserAsync(User user, string password);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<User?> GetCurrentUserAsync();
        void SetCurrentUser(User user);
        void Logout();
        bool IsAuthenticated { get; }
        User? CurrentUser { get; }
    }
}
