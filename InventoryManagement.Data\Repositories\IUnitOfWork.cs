using InventoryManagement.Models;

namespace InventoryManagement.Data.Repositories
{
    public interface IUnitOfWork : IDisposable
    {
        IRepository<User> Users { get; }
        IRepository<Category> Categories { get; }
        IRepository<Supplier> Suppliers { get; }
        IRepository<Customer> Customers { get; }
        IRepository<Product> Products { get; }
        IRepository<PurchaseOrder> PurchaseOrders { get; }
        IRepository<PurchaseOrderItem> PurchaseOrderItems { get; }
        IRepository<SalesOrder> SalesOrders { get; }
        IRepository<SalesOrderItem> SalesOrderItems { get; }
        IRepository<StockMovement> StockMovements { get; }
        IRepository<StockAdjustment> StockAdjustments { get; }
        IRepository<AuditLog> AuditLogs { get; }
        IRepository<Notification> Notifications { get; }
        
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
