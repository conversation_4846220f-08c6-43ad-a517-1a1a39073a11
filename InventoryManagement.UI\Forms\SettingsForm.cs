using Microsoft.Extensions.DependencyInjection;

namespace InventoryManagement.UI.Forms
{
    public partial class SettingsForm : Form
    {
        private readonly IServiceProvider _serviceProvider;

        public SettingsForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }
    }

    partial class SettingsForm
    {
        private void InitializeComponent()
        {
            var label = new Label
            {
                Text = "System Settings - Coming Soon",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            
            this.Controls.Add(label);
            this.Text = "System Settings";
            this.Size = new Size(800, 600);
        }
    }
}
