using Microsoft.Extensions.DependencyInjection;
using InventoryManagement.Business.Services;
using InventoryManagement.Models;
using InventoryManagement.Data.Repositories;

namespace InventoryManagement.UI.Forms
{
    public partial class ProductsForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IProductService _productService;
        private readonly IUnitOfWork _unitOfWork;
        private List<Product> _products = new List<Product>();
        private List<Category> _categories = new List<Category>();
        private List<Supplier> _suppliers = new List<Supplier>();

        public ProductsForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _productService = serviceProvider.GetRequiredService<IProductService>();
            _unitOfWork = serviceProvider.GetRequiredService<IUnitOfWork>();
            InitializeComponent();
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                await LoadProducts();
                await LoadCategories();
                await LoadSuppliers();
                SetupDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadProducts()
        {
            _products = (await _productService.GetAllProductsAsync()).ToList();
            dgvProducts.DataSource = _products.Select(p => new
            {
                p.ProductId,
                p.SKU,
                p.Name,
                Category = _categories.FirstOrDefault(c => c.CategoryId == p.CategoryId)?.Name ?? "Unknown",
                Supplier = _suppliers.FirstOrDefault(s => s.SupplierId == p.SupplierId)?.Name ?? "None",
                p.CurrentStock,
                p.ReorderLevel,
                p.CostPrice,
                p.SellingPrice,
                p.IsActive
            }).ToList();
        }

        private async Task LoadCategories()
        {
            _categories = (await _unitOfWork.Categories.GetAllAsync()).ToList();
            cmbCategory.DataSource = _categories;
            cmbCategory.DisplayMember = "Name";
            cmbCategory.ValueMember = "CategoryId";
            cmbCategory.SelectedIndex = -1;
        }

        private async Task LoadSuppliers()
        {
            _suppliers = (await _unitOfWork.Suppliers.GetAllAsync()).ToList();
            cmbSupplier.DataSource = _suppliers;
            cmbSupplier.DisplayMember = "Name";
            cmbSupplier.ValueMember = "SupplierId";
            cmbSupplier.SelectedIndex = -1;
        }

        private void SetupDataGridView()
        {
            dgvProducts.AutoGenerateColumns = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = false;
            dgvProducts.ReadOnly = true;
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var form = new ProductEditForm(_serviceProvider);
            if (form.ShowDialog() == DialogResult.OK)
            {
                await LoadProducts();
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                var productId = (int)dgvProducts.SelectedRows[0].Cells["ProductId"].Value;
                var product = await _productService.GetProductByIdAsync(productId);
                
                if (product != null)
                {
                    var form = new ProductEditForm(_serviceProvider, product);
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        await LoadProducts();
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a product to edit.", "No Selection", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Are you sure you want to delete this product?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        var productId = (int)dgvProducts.SelectedRows[0].Cells["ProductId"].Value;
                        await _productService.DeleteProductAsync(productId);
                        await LoadProducts();
                        MessageBox.Show("Product deleted successfully.", "Success", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting product: {ex.Message}", "Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a product to delete.", "No Selection", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                await LoadProducts();
            }
            else
            {
                var searchResults = await _productService.SearchProductsAsync(txtSearch.Text);
                dgvProducts.DataSource = searchResults.Select(p => new
                {
                    p.ProductId,
                    p.SKU,
                    p.Name,
                    Category = _categories.FirstOrDefault(c => c.CategoryId == p.CategoryId)?.Name ?? "Unknown",
                    Supplier = _suppliers.FirstOrDefault(s => s.SupplierId == p.SupplierId)?.Name ?? "None",
                    p.CurrentStock,
                    p.ReorderLevel,
                    p.CostPrice,
                    p.SellingPrice,
                    p.IsActive
                }).ToList();
            }
        }

        private async void cmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCategory.SelectedValue != null && cmbCategory.SelectedIndex >= 0)
            {
                var categoryId = (int)cmbCategory.SelectedValue;
                var products = await _productService.GetProductsByCategoryAsync(categoryId);
                dgvProducts.DataSource = products.Select(p => new
                {
                    p.ProductId,
                    p.SKU,
                    p.Name,
                    Category = _categories.FirstOrDefault(c => c.CategoryId == p.CategoryId)?.Name ?? "Unknown",
                    Supplier = _suppliers.FirstOrDefault(s => s.SupplierId == p.SupplierId)?.Name ?? "None",
                    p.CurrentStock,
                    p.ReorderLevel,
                    p.CostPrice,
                    p.SellingPrice,
                    p.IsActive
                }).ToList();
            }
        }

        private async void btnShowAll_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCategory.SelectedIndex = -1;
            cmbSupplier.SelectedIndex = -1;
            await LoadProducts();
        }

        private async void btnLowStock_Click(object sender, EventArgs e)
        {
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            dgvProducts.DataSource = lowStockProducts.Select(p => new
            {
                p.ProductId,
                p.SKU,
                p.Name,
                Category = _categories.FirstOrDefault(c => c.CategoryId == p.CategoryId)?.Name ?? "Unknown",
                Supplier = _suppliers.FirstOrDefault(s => s.SupplierId == p.SupplierId)?.Name ?? "None",
                p.CurrentStock,
                p.ReorderLevel,
                p.CostPrice,
                p.SellingPrice,
                p.IsActive
            }).ToList();
        }

        private void dgvProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }
    }
}
