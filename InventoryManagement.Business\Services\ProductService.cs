using InventoryManagement.Data.Repositories;
using InventoryManagement.Models;

namespace InventoryManagement.Business.Services
{
    public class ProductService : IProductService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAuthService _authService;

        public ProductService(IUnitOfWork unitOfWork, IAuthService authService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
        }

        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            return await _unitOfWork.Products.GetAllAsync();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            return await _unitOfWork.Products.GetByIdAsync(id);
        }

        public async Task<Product?> GetProductBySkuAsync(string sku)
        {
            return await _unitOfWork.Products.FirstOrDefaultAsync(p => p.SKU == sku);
        }

        public async Task<Product?> GetProductByBarcodeAsync(string barcode)
        {
            return await _unitOfWork.Products.FirstOrDefaultAsync(p => p.Barcode == barcode);
        }

        public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            return await _unitOfWork.Products.FindAsync(p => p.CategoryId == categoryId && p.IsActive);
        }

        public async Task<IEnumerable<Product>> GetProductsBySupplierAsync(int supplierId)
        {
            return await _unitOfWork.Products.FindAsync(p => p.SupplierId == supplierId && p.IsActive);
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _unitOfWork.Products.FindAsync(p => 
                p.TrackStock && p.CurrentStock <= p.ReorderLevel && p.IsActive);
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
        {
            return await _unitOfWork.Products.FindAsync(p => 
                (p.Name.Contains(searchTerm) || 
                 p.SKU.Contains(searchTerm) || 
                 (p.Barcode != null && p.Barcode.Contains(searchTerm)) ||
                 (p.Description != null && p.Description.Contains(searchTerm))) && 
                p.IsActive);
        }

        public async Task<Product> CreateProductAsync(Product product)
        {
            // Validate SKU uniqueness
            if (await IsSkuUniqueAsync(product.SKU) == false)
                throw new InvalidOperationException("SKU already exists");

            // Validate barcode uniqueness if provided
            if (!string.IsNullOrEmpty(product.Barcode) && await IsBarcodeUniqueAsync(product.Barcode) == false)
                throw new InvalidOperationException("Barcode already exists");

            product.CreatedDate = DateTime.UtcNow;
            product.CreatedBy = _authService.CurrentUser?.Username;

            var createdProduct = await _unitOfWork.Products.AddAsync(product);

            // Create initial stock movement if stock > 0
            if (product.CurrentStock > 0)
            {
                await CreateStockMovementAsync(createdProduct.ProductId, MovementType.StockIn, 
                    product.CurrentStock, 0, product.CurrentStock, "Initial Stock", "Product creation");
            }

            return createdProduct;
        }

        public async Task<Product> UpdateProductAsync(Product product)
        {
            var existingProduct = await _unitOfWork.Products.GetByIdAsync(product.ProductId);
            if (existingProduct == null)
                throw new InvalidOperationException("Product not found");

            // Validate SKU uniqueness
            if (await IsSkuUniqueAsync(product.SKU, product.ProductId) == false)
                throw new InvalidOperationException("SKU already exists");

            // Validate barcode uniqueness if provided
            if (!string.IsNullOrEmpty(product.Barcode) && 
                await IsBarcodeUniqueAsync(product.Barcode, product.ProductId) == false)
                throw new InvalidOperationException("Barcode already exists");

            // Track stock changes
            if (existingProduct.CurrentStock != product.CurrentStock)
            {
                var movementType = product.CurrentStock > existingProduct.CurrentStock ? 
                    MovementType.StockIn : MovementType.StockOut;
                var quantity = Math.Abs(product.CurrentStock - existingProduct.CurrentStock);

                await CreateStockMovementAsync(product.ProductId, movementType, 
                    quantity, existingProduct.CurrentStock, product.CurrentStock, 
                    "Manual Adjustment", "Stock updated via product edit");
            }

            product.LastUpdated = DateTime.UtcNow;
            await _unitOfWork.Products.UpdateAsync(product);
            return product;
        }

        public async Task DeleteProductAsync(int id)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(id);
            if (product == null)
                throw new InvalidOperationException("Product not found");

            // Soft delete - just mark as inactive
            product.IsActive = false;
            await _unitOfWork.Products.UpdateAsync(product);
        }

        public async Task<bool> IsSkuUniqueAsync(string sku, int? excludeProductId = null)
        {
            if (excludeProductId.HasValue)
            {
                return !await _unitOfWork.Products.ExistsAsync(p => p.SKU == sku && p.ProductId != excludeProductId.Value);
            }
            return !await _unitOfWork.Products.ExistsAsync(p => p.SKU == sku);
        }

        public async Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeProductId = null)
        {
            if (excludeProductId.HasValue)
            {
                return !await _unitOfWork.Products.ExistsAsync(p => p.Barcode == barcode && p.ProductId != excludeProductId.Value);
            }
            return !await _unitOfWork.Products.ExistsAsync(p => p.Barcode == barcode);
        }

        public async Task UpdateStockAsync(int productId, int quantity, MovementType movementType, 
            string? reference = null, string? notes = null)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            if (product == null)
                throw new InvalidOperationException("Product not found");

            var previousStock = product.CurrentStock;
            var newStock = movementType == MovementType.StockIn ? 
                previousStock + quantity : previousStock - quantity;

            if (newStock < 0)
                throw new InvalidOperationException("Insufficient stock");

            product.CurrentStock = newStock;
            product.LastUpdated = DateTime.UtcNow;
            await _unitOfWork.Products.UpdateAsync(product);

            await CreateStockMovementAsync(productId, movementType, quantity, 
                previousStock, newStock, reference, notes);
        }

        public async Task<string> GenerateSkuAsync(int categoryId)
        {
            var category = await _unitOfWork.Categories.GetByIdAsync(categoryId);
            var categoryPrefix = category?.Name.Substring(0, Math.Min(3, category.Name.Length)).ToUpper() ?? "PRD";
            
            var lastProduct = (await _unitOfWork.Products.FindAsync(p => p.CategoryId == categoryId))
                .OrderByDescending(p => p.ProductId).FirstOrDefault();
            
            var nextNumber = (lastProduct?.ProductId ?? 0) + 1;
            return $"{categoryPrefix}{nextNumber:D6}";
        }

        public async Task<string> GenerateBarcodeAsync()
        {
            string barcode;
            do
            {
                barcode = GenerateRandomBarcode();
            } while (await _unitOfWork.Products.ExistsAsync(p => p.Barcode == barcode));
            
            return barcode;
        }

        private async Task CreateStockMovementAsync(int productId, MovementType movementType, 
            int quantity, int previousStock, int newStock, string? reference, string? notes)
        {
            var stockMovement = new StockMovement
            {
                ProductId = productId,
                MovementType = movementType,
                Quantity = quantity,
                PreviousStock = previousStock,
                NewStock = newStock,
                Reference = reference,
                Notes = notes,
                MovementDate = DateTime.UtcNow,
                CreatedBy = _authService.CurrentUser?.Username
            };

            await _unitOfWork.StockMovements.AddAsync(stockMovement);
        }

        private string GenerateRandomBarcode()
        {
            var random = new Random();
            return random.Next(100000000, 999999999).ToString() + 
                   random.Next(100, 999).ToString();
        }
    }
}
