using InventoryManagement.Models;

namespace InventoryManagement.Business.Services
{
    public interface IProductService
    {
        Task<IEnumerable<Product>> GetAllProductsAsync();
        Task<Product?> GetProductByIdAsync(int id);
        Task<Product?> GetProductBySkuAsync(string sku);
        Task<Product?> GetProductByBarcodeAsync(string barcode);
        Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId);
        Task<IEnumerable<Product>> GetProductsBySupplierAsync(int supplierId);
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);
        Task<Product> CreateProductAsync(Product product);
        Task<Product> UpdateProductAsync(Product product);
        Task DeleteProductAsync(int id);
        Task<bool> IsSkuUniqueAsync(string sku, int? excludeProductId = null);
        Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeProductId = null);
        Task UpdateStockAsync(int productId, int quantity, MovementType movementType, string? reference = null, string? notes = null);
        Task<string> GenerateSkuAsync(int categoryId);
        Task<string> GenerateBarcodeAsync();
    }
}
