# Complete Setup Guide - Inventory Management System

## Step-by-Step Installation Guide

### Step 1: Install Prerequisites

#### 1.1 Install .NET 8 SDK
1. Go to https://dotnet.microsoft.com/download
2. Download .NET 8 SDK for Windows
3. Run the installer and follow the instructions
4. Verify installation by opening Command Prompt and typing: `dotnet --version`

#### 1.2 Install PostgreSQL
1. Go to https://www.postgresql.org/download/windows/
2. Download PostgreSQL installer (latest version)
3. Run the installer
4. **IMPORTANT**: Remember the password you set for the `postgres` user
5. Keep default port `5432`
6. Install pgAdmin (database management tool) when prompted

#### 1.3 Install Visual Studio 2022 (Recommended)
1. Go to https://visualstudio.microsoft.com/downloads/
2. Download Visual Studio 2022 Community (free)
3. During installation, select:
   - .NET desktop development workload
   - ASP.NET and web development workload

### Step 2: Setup Database

#### 2.1 Verify PostgreSQL Installation
1. Open pgAdmin
2. Connect to PostgreSQL server using the password you set
3. You should see the PostgreSQL server running

#### 2.2 Note Your Database Credentials
- Host: `localhost`
- Port: `5432`
- Username: `postgres`
- Password: [The password you set during installation]
- Database: `InventoryManagementDB` (will be created automatically)

### Step 3: Configure the Application

#### 3.1 Update Connection String
1. Navigate to the project folder
2. Open `InventoryManagement.UI/appsettings.json`
3. Replace `your_password_here` with your actual PostgreSQL password:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=InventoryManagementDB;Username=postgres;Password=YOUR_ACTUAL_PASSWORD"
  }
}
```

### Step 4: Build and Run the Application

#### 4.1 Open the Solution
1. Open Visual Studio 2022
2. Click "Open a project or solution"
3. Navigate to your project folder
4. Select `InventoryManagement.sln`

#### 4.2 Restore NuGet Packages
1. In Visual Studio, right-click on the solution in Solution Explorer
2. Select "Restore NuGet Packages"
3. Wait for all packages to be restored

#### 4.3 Build the Solution
1. Press `Ctrl+Shift+B` or go to Build → Build Solution
2. Ensure there are no build errors
3. If there are errors, check that all NuGet packages are restored

#### 4.4 Set Startup Project
1. Right-click on `InventoryManagement.UI` project in Solution Explorer
2. Select "Set as Startup Project"

#### 4.5 Run the Application
1. Press `F5` or click the "Start" button
2. The application will:
   - Create the database automatically
   - Seed initial data (admin user and sample categories)
   - Open the login form

### Step 5: First Login

#### 5.1 Login Credentials
- **Username**: `admin`
- **Password**: `admin123`

#### 5.2 Verify Installation
1. After login, you should see the main dashboard
2. Check that the sidebar menu is visible
3. Try clicking on "Products" to verify database connectivity

### Step 6: Initial Setup (Optional)

#### 6.1 Add Categories
1. Click on "Categories" in the sidebar
2. Add product categories relevant to your business

#### 6.2 Add Suppliers
1. Click on "Suppliers" in the sidebar
2. Add your suppliers' information

#### 6.3 Add Your First Product
1. Click on "Products" in the sidebar
2. Click "Add" button
3. Fill in product details
4. Use "Generate" buttons for SKU and Barcode

## Troubleshooting Common Issues

### Issue 1: Database Connection Failed
**Error**: "Unable to connect to database"

**Solutions**:
1. Verify PostgreSQL is running:
   - Open Services (Windows + R, type `services.msc`)
   - Look for "postgresql-x64-xx" service
   - Ensure it's running

2. Check connection string:
   - Verify password is correct
   - Ensure no extra spaces in the connection string

3. Test connection manually:
   - Open pgAdmin
   - Try connecting with the same credentials

### Issue 2: Build Errors
**Error**: Various build/compilation errors

**Solutions**:
1. Clean and rebuild:
   - Build → Clean Solution
   - Build → Rebuild Solution

2. Restore NuGet packages:
   - Tools → NuGet Package Manager → Package Manager Console
   - Run: `Update-Package -reinstall`

3. Check .NET version:
   - Ensure .NET 8 SDK is installed
   - Project should target .NET 8

### Issue 3: Login Not Working
**Error**: "Invalid username or password"

**Solutions**:
1. Verify database was created:
   - Check pgAdmin for `InventoryManagementDB` database
   - Look for `Users` table with admin user

2. Reset admin password:
   - The system creates admin user automatically on first run
   - If issues persist, delete the database and restart the application

### Issue 4: Forms Not Loading
**Error**: Forms appear blank or don't load properly

**Solutions**:
1. Check for missing references
2. Ensure all projects are built successfully
3. Verify all NuGet packages are restored

## Advanced Configuration

### Changing Database Settings
To use a different database server or credentials:

1. Update `appsettings.json` with new connection string
2. Ensure the PostgreSQL server is accessible
3. The application will create the database automatically

### Adding New Users
1. Login as admin
2. Navigate to Settings (when implemented)
3. Or add users directly through the database

### Backup and Restore
1. Use pgAdmin to backup the database
2. Export data as SQL script
3. Restore by running the script on a new database

## Performance Tips

1. **Regular Maintenance**:
   - Regularly backup your database
   - Monitor disk space usage
   - Keep PostgreSQL updated

2. **Optimization**:
   - Index frequently searched columns
   - Archive old data periodically
   - Monitor query performance

3. **Security**:
   - Change default admin password
   - Use strong passwords for database
   - Regularly update the application

## Getting Help

If you encounter issues not covered in this guide:

1. Check the error messages carefully
2. Verify all prerequisites are installed correctly
3. Ensure database connectivity
4. Review the README.md for additional information

## Next Steps

Once the system is running:

1. Explore all the features
2. Set up your product catalog
3. Configure suppliers and customers
4. Start processing orders
5. Monitor inventory levels
6. Generate reports

The system is designed to be intuitive and user-friendly. Take time to explore each module and customize it according to your business needs.
