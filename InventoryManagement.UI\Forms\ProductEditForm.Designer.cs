namespace InventoryManagement.UI.Forms
{
    partial class ProductEditForm
    {
        private System.ComponentModel.IContainer components = null;
        private TextBox txtSKU, txtName, txtDescription, txtBarcode, txtUnit, txtDimensions;
        private ComboBox cmbCategory, cmbSupplier;
        private NumericUpDown numCostPrice, numSellingPrice, numWholesalePrice, numCurrentStock;
        private NumericUpDown numMinStock, numMaxStock, numReorderLevel, numWeight, numTaxRate;
        private CheckBox chkIsActive, chkTrackStock;
        private Button btnSave, btnCancel, btnGenerateSKU, btnGenerateBarcode;
        private Label lblSKU, lblName, lblDescription, lblCategory, lblSupplier, lblBarcode;
        private Label lblCostPrice, lblSellingPrice, lblWholesalePrice, lblCurrentStock;
        private Label lblMinStock, lblMaxStock, lblReorderLevel, lblUnit, lblWeight;
        private Label lblDimensions, lblTaxRate;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            // Initialize all controls
            this.txtSKU = new TextBox();
            this.txtName = new TextBox();
            this.txtDescription = new TextBox();
            this.txtBarcode = new TextBox();
            this.txtUnit = new TextBox();
            this.txtDimensions = new TextBox();
            this.cmbCategory = new ComboBox();
            this.cmbSupplier = new ComboBox();
            this.numCostPrice = new NumericUpDown();
            this.numSellingPrice = new NumericUpDown();
            this.numWholesalePrice = new NumericUpDown();
            this.numCurrentStock = new NumericUpDown();
            this.numMinStock = new NumericUpDown();
            this.numMaxStock = new NumericUpDown();
            this.numReorderLevel = new NumericUpDown();
            this.numWeight = new NumericUpDown();
            this.numTaxRate = new NumericUpDown();
            this.chkIsActive = new CheckBox();
            this.chkTrackStock = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnGenerateSKU = new Button();
            this.btnGenerateBarcode = new Button();

            // Initialize labels
            this.lblSKU = new Label();
            this.lblName = new Label();
            this.lblDescription = new Label();
            this.lblCategory = new Label();
            this.lblSupplier = new Label();
            this.lblBarcode = new Label();
            this.lblCostPrice = new Label();
            this.lblSellingPrice = new Label();
            this.lblWholesalePrice = new Label();
            this.lblCurrentStock = new Label();
            this.lblMinStock = new Label();
            this.lblMaxStock = new Label();
            this.lblReorderLevel = new Label();
            this.lblUnit = new Label();
            this.lblWeight = new Label();
            this.lblDimensions = new Label();
            this.lblTaxRate = new Label();

            this.SuspendLayout();

            // Configure form
            this.Text = "Product Details";
            this.Size = new Size(600, 700);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;

            int yPos = 20;
            int labelWidth = 120;
            int controlWidth = 200;
            int leftMargin = 20;
            int rightMargin = 300;

            // SKU
            SetupLabelAndControl(lblSKU, txtSKU, "SKU:", yPos, labelWidth, controlWidth, leftMargin);
            btnGenerateSKU.Location = new Point(rightMargin + controlWidth + 10, yPos);
            btnGenerateSKU.Size = new Size(80, 23);
            btnGenerateSKU.Text = "Generate";
            btnGenerateSKU.Click += btnGenerateSKU_Click;
            this.Controls.Add(btnGenerateSKU);
            yPos += 35;

            // Name
            SetupLabelAndControl(lblName, txtName, "Name:", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;

            // Description
            SetupLabelAndControl(lblDescription, txtDescription, "Description:", yPos, labelWidth, controlWidth, leftMargin);
            txtDescription.Multiline = true;
            txtDescription.Height = 60;
            yPos += 75;

            // Category
            SetupLabelAndControl(lblCategory, cmbCategory, "Category:", yPos, labelWidth, controlWidth, leftMargin);
            cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            yPos += 35;

            // Supplier
            SetupLabelAndControl(lblSupplier, cmbSupplier, "Supplier:", yPos, labelWidth, controlWidth, leftMargin);
            cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList;
            yPos += 35;

            // Barcode
            SetupLabelAndControl(lblBarcode, txtBarcode, "Barcode:", yPos, labelWidth, controlWidth, leftMargin);
            btnGenerateBarcode.Location = new Point(rightMargin + controlWidth + 10, yPos);
            btnGenerateBarcode.Size = new Size(80, 23);
            btnGenerateBarcode.Text = "Generate";
            btnGenerateBarcode.Click += btnGenerateBarcode_Click;
            this.Controls.Add(btnGenerateBarcode);
            yPos += 35;

            // Prices
            SetupLabelAndNumeric(lblCostPrice, numCostPrice, "Cost Price:", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;
            SetupLabelAndNumeric(lblSellingPrice, numSellingPrice, "Selling Price:", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;
            SetupLabelAndNumeric(lblWholesalePrice, numWholesalePrice, "Wholesale Price:", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;

            // Stock
            SetupLabelAndNumeric(lblCurrentStock, numCurrentStock, "Current Stock:", yPos, labelWidth, controlWidth, leftMargin, 0, 999999, 0);
            yPos += 35;
            SetupLabelAndNumeric(lblMinStock, numMinStock, "Min Stock:", yPos, labelWidth, controlWidth, leftMargin, 0, 999999, 0);
            yPos += 35;
            SetupLabelAndNumeric(lblMaxStock, numMaxStock, "Max Stock:", yPos, labelWidth, controlWidth, leftMargin, 0, 999999, 1000);
            yPos += 35;
            SetupLabelAndNumeric(lblReorderLevel, numReorderLevel, "Reorder Level:", yPos, labelWidth, controlWidth, leftMargin, 0, 999999, 10);
            yPos += 35;

            // Unit
            SetupLabelAndControl(lblUnit, txtUnit, "Unit:", yPos, labelWidth, controlWidth, leftMargin);
            txtUnit.Text = "PCS";
            yPos += 35;

            // Weight
            SetupLabelAndNumeric(lblWeight, numWeight, "Weight (kg):", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;

            // Dimensions
            SetupLabelAndControl(lblDimensions, txtDimensions, "Dimensions:", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;

            // Tax Rate
            SetupLabelAndNumeric(lblTaxRate, numTaxRate, "Tax Rate (%):", yPos, labelWidth, controlWidth, leftMargin);
            yPos += 35;

            // Checkboxes
            chkIsActive.Location = new Point(leftMargin, yPos);
            chkIsActive.Size = new Size(100, 23);
            chkIsActive.Text = "Is Active";
            chkIsActive.Checked = true;
            this.Controls.Add(chkIsActive);

            chkTrackStock.Location = new Point(leftMargin + 120, yPos);
            chkTrackStock.Size = new Size(120, 23);
            chkTrackStock.Text = "Track Stock";
            chkTrackStock.Checked = true;
            this.Controls.Add(chkTrackStock);
            yPos += 40;

            // Buttons
            btnSave.Location = new Point(leftMargin, yPos);
            btnSave.Size = new Size(100, 35);
            btnSave.Text = "Save";
            btnSave.BackColor = Color.FromArgb(40, 167, 69);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Click += btnSave_Click;
            this.Controls.Add(btnSave);

            btnCancel.Location = new Point(leftMargin + 120, yPos);
            btnCancel.Size = new Size(100, 35);
            btnCancel.Text = "Cancel";
            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Click += btnCancel_Click;
            this.Controls.Add(btnCancel);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupLabelAndControl(Label label, Control control, string text, int yPos, int labelWidth, int controlWidth, int leftMargin)
        {
            label.Location = new Point(leftMargin, yPos);
            label.Size = new Size(labelWidth, 23);
            label.Text = text;
            label.TextAlign = ContentAlignment.MiddleLeft;
            this.Controls.Add(label);

            control.Location = new Point(leftMargin + labelWidth + 10, yPos);
            control.Size = new Size(controlWidth, 23);
            this.Controls.Add(control);
        }

        private void SetupLabelAndNumeric(Label label, NumericUpDown numeric, string text, int yPos, int labelWidth, int controlWidth, int leftMargin, decimal min = 0, decimal max = 999999, decimal defaultValue = 0)
        {
            SetupLabelAndControl(label, numeric, text, yPos, labelWidth, controlWidth, leftMargin);
            numeric.DecimalPlaces = 2;
            numeric.Minimum = min;
            numeric.Maximum = max;
            numeric.Value = defaultValue;
        }
    }
}
