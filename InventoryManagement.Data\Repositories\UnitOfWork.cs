using Microsoft.EntityFrameworkCore.Storage;
using InventoryManagement.Models;

namespace InventoryManagement.Data.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly InventoryDbContext _context;
        private IDbContextTransaction? _transaction;

        public UnitOfWork(InventoryDbContext context)
        {
            _context = context;
            Users = new Repository<User>(_context);
            Categories = new Repository<Category>(_context);
            Suppliers = new Repository<Supplier>(_context);
            Customers = new Repository<Customer>(_context);
            Products = new Repository<Product>(_context);
            PurchaseOrders = new Repository<PurchaseOrder>(_context);
            PurchaseOrderItems = new Repository<PurchaseOrderItem>(_context);
            SalesOrders = new Repository<SalesOrder>(_context);
            SalesOrderItems = new Repository<SalesOrderItem>(_context);
            StockMovements = new Repository<StockMovement>(_context);
            StockAdjustments = new Repository<StockAdjustment>(_context);
            AuditLogs = new Repository<AuditLog>(_context);
            Notifications = new Repository<Notification>(_context);
        }

        public IRepository<User> Users { get; private set; }
        public IRepository<Category> Categories { get; private set; }
        public IRepository<Supplier> Suppliers { get; private set; }
        public IRepository<Customer> Customers { get; private set; }
        public IRepository<Product> Products { get; private set; }
        public IRepository<PurchaseOrder> PurchaseOrders { get; private set; }
        public IRepository<PurchaseOrderItem> PurchaseOrderItems { get; private set; }
        public IRepository<SalesOrder> SalesOrders { get; private set; }
        public IRepository<SalesOrderItem> SalesOrderItems { get; private set; }
        public IRepository<StockMovement> StockMovements { get; private set; }
        public IRepository<StockAdjustment> StockAdjustments { get; private set; }
        public IRepository<AuditLog> AuditLogs { get; private set; }
        public IRepository<Notification> Notifications { get; private set; }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
