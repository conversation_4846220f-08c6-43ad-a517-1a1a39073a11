using Microsoft.Extensions.DependencyInjection;

namespace InventoryManagement.UI.Forms
{
    public partial class CategoriesForm : Form
    {
        private readonly IServiceProvider _serviceProvider;

        public CategoriesForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }
    }

    partial class CategoriesForm
    {
        private void InitializeComponent()
        {
            var label = new Label
            {
                Text = "Categories Management - Coming Soon",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            
            this.Controls.Add(label);
            this.Text = "Categories Management";
            this.Size = new Size(800, 600);
        }
    }
}
