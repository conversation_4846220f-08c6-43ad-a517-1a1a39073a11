using InventoryManagement.Data.Repositories;
using InventoryManagement.Models;
using BCrypt.Net;

namespace InventoryManagement.Business.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private User? _currentUser;

        public AuthService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public bool IsAuthenticated => _currentUser != null;
        public User? CurrentUser => _currentUser;

        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => 
                u.Username == username && u.IsActive);

            if (user != null && BCrypt.Verify(password, user.PasswordHash))
            {
                user.LastLoginDate = DateTime.UtcNow;
                await _unitOfWork.Users.UpdateAsync(user);
                
                _currentUser = user;
                return user;
            }

            return null;
        }

        public async Task<User> RegisterUserAsync(User user, string password)
        {
            // Check if username already exists
            var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == user.Username);
            if (existingUser != null)
                throw new InvalidOperationException("Username already exists");

            // Check if email already exists
            existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == user.Email);
            if (existingUser != null)
                throw new InvalidOperationException("Email already exists");

            // Hash password
            user.PasswordHash = BCrypt.HashPassword(password);
            user.CreatedDate = DateTime.UtcNow;

            return await _unitOfWork.Users.AddAsync(user);
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null || !BCrypt.Verify(currentPassword, user.PasswordHash))
                return false;

            user.PasswordHash = BCrypt.HashPassword(newPassword);
            await _unitOfWork.Users.UpdateAsync(user);
            return true;
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            if (_currentUser != null)
            {
                return await _unitOfWork.Users.GetByIdAsync(_currentUser.UserId);
            }
            return null;
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
        }

        public void Logout()
        {
            _currentUser = null;
        }
    }
}
