using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    public class AuditLog
    {
        public int AuditLogId { get; set; }
        
        public int UserId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string TableName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Operation { get; set; } = string.Empty;
        
        public int? RecordId { get; set; }
        
        public string? OldValues { get; set; }
        
        public string? NewValues { get; set; }
        
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        [StringLength(45)]
        public string? IPAddress { get; set; }
        
        [StringLength(500)]
        public string? UserAgent { get; set; }
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
    }

    public class Notification
    {
        public int NotificationId { get; set; }
        
        public int? UserId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [StringLength(1000)]
        public string Message { get; set; } = string.Empty;
        
        public NotificationType Type { get; set; }
        
        public bool IsRead { get; set; } = false;
        
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ReadDate { get; set; }
        
        // Navigation properties
        public virtual User? User { get; set; }
    }

    public enum NotificationType
    {
        Info = 1,
        Warning = 2,
        Error = 3,
        LowStock = 4,
        OrderUpdate = 5,
        SystemAlert = 6
    }
}
