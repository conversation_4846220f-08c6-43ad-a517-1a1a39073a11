using Microsoft.Extensions.DependencyInjection;
using InventoryManagement.Business.Services;
using InventoryManagement.Models;
using InventoryManagement.Data.Repositories;

namespace InventoryManagement.UI.Forms
{
    public partial class ProductEditForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IProductService _productService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly Product? _product;
        private readonly bool _isEditMode;

        public ProductEditForm(IServiceProvider serviceProvider, Product? product = null)
        {
            _serviceProvider = serviceProvider;
            _productService = serviceProvider.GetRequiredService<IProductService>();
            _unitOfWork = serviceProvider.GetRequiredService<IUnitOfWork>();
            _product = product;
            _isEditMode = product != null;
            
            InitializeComponent();
            LoadData();
            
            if (_isEditMode)
            {
                LoadProductData();
                this.Text = "Edit Product";
            }
            else
            {
                this.Text = "Add New Product";
            }
        }

        private async void LoadData()
        {
            try
            {
                // Load categories
                var categories = await _unitOfWork.Categories.FindAsync(c => c.IsActive);
                cmbCategory.DataSource = categories.ToList();
                cmbCategory.DisplayMember = "Name";
                cmbCategory.ValueMember = "CategoryId";

                // Load suppliers
                var suppliers = await _unitOfWork.Suppliers.FindAsync(s => s.IsActive);
                var supplierList = suppliers.ToList();
                supplierList.Insert(0, new Supplier { SupplierId = 0, Name = "-- No Supplier --" });
                cmbSupplier.DataSource = supplierList;
                cmbSupplier.DisplayMember = "Name";
                cmbSupplier.ValueMember = "SupplierId";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductData()
        {
            if (_product != null)
            {
                txtSKU.Text = _product.SKU;
                txtName.Text = _product.Name;
                txtDescription.Text = _product.Description;
                txtBarcode.Text = _product.Barcode;
                numCostPrice.Value = _product.CostPrice;
                numSellingPrice.Value = _product.SellingPrice;
                numWholesalePrice.Value = _product.WholesalePrice ?? 0;
                numCurrentStock.Value = _product.CurrentStock;
                numMinStock.Value = _product.MinimumStock;
                numMaxStock.Value = _product.MaximumStock;
                numReorderLevel.Value = _product.ReorderLevel;
                txtUnit.Text = _product.Unit;
                numWeight.Value = (decimal)(_product.Weight ?? 0);
                txtDimensions.Text = _product.Dimensions;
                numTaxRate.Value = _product.TaxRate;
                chkIsActive.Checked = _product.IsActive;
                chkTrackStock.Checked = _product.TrackStock;
                
                cmbCategory.SelectedValue = _product.CategoryId;
                cmbSupplier.SelectedValue = _product.SupplierId ?? 0;
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "Saving...";

                var product = _isEditMode ? _product! : new Product();
                
                product.SKU = txtSKU.Text.Trim();
                product.Name = txtName.Text.Trim();
                product.Description = txtDescription.Text.Trim();
                product.CategoryId = (int)cmbCategory.SelectedValue;
                product.SupplierId = (int)cmbSupplier.SelectedValue == 0 ? null : (int)cmbSupplier.SelectedValue;
                product.Barcode = string.IsNullOrWhiteSpace(txtBarcode.Text) ? null : txtBarcode.Text.Trim();
                product.CostPrice = numCostPrice.Value;
                product.SellingPrice = numSellingPrice.Value;
                product.WholesalePrice = numWholesalePrice.Value == 0 ? null : numWholesalePrice.Value;
                product.CurrentStock = (int)numCurrentStock.Value;
                product.MinimumStock = (int)numMinStock.Value;
                product.MaximumStock = (int)numMaxStock.Value;
                product.ReorderLevel = (int)numReorderLevel.Value;
                product.Unit = txtUnit.Text.Trim();
                product.Weight = numWeight.Value == 0 ? null : (double)numWeight.Value;
                product.Dimensions = string.IsNullOrWhiteSpace(txtDimensions.Text) ? null : txtDimensions.Text.Trim();
                product.TaxRate = numTaxRate.Value;
                product.IsActive = chkIsActive.Checked;
                product.TrackStock = chkTrackStock.Checked;

                if (_isEditMode)
                {
                    await _productService.UpdateProductAsync(product);
                    MessageBox.Show("Product updated successfully!", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _productService.CreateProductAsync(product);
                    MessageBox.Show("Product created successfully!", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving product: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "Save";
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtSKU.Text))
            {
                MessageBox.Show("SKU is required.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSKU.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("Product name is required.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (cmbCategory.SelectedValue == null)
            {
                MessageBox.Show("Please select a category.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategory.Focus();
                return false;
            }

            if (numSellingPrice.Value <= 0)
            {
                MessageBox.Show("Selling price must be greater than 0.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numSellingPrice.Focus();
                return false;
            }

            return true;
        }

        private async void btnGenerateSKU_Click(object sender, EventArgs e)
        {
            if (cmbCategory.SelectedValue != null)
            {
                try
                {
                    var sku = await _productService.GenerateSkuAsync((int)cmbCategory.SelectedValue);
                    txtSKU.Text = sku;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error generating SKU: {ex.Message}", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Please select a category first.", "Information", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void btnGenerateBarcode_Click(object sender, EventArgs e)
        {
            try
            {
                var barcode = await _productService.GenerateBarcodeAsync();
                txtBarcode.Text = barcode;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating barcode: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
